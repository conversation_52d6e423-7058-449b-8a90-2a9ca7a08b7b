import React from 'react';
import { Link } from 'react-router-dom';
import ScholarshipCard from './ScholarshipCard';
import { Scholarship } from './ScholarshipGrid';
import { Government, Building, Organization } from './icons';

interface FundingSourceSectionProps {
  governmentScholarships: Scholarship[];
  universityScholarships: Scholarship[];
  organizationScholarships: Scholarship[];
  loading: boolean;
  onScholarshipClick: (id: number) => void;
}

const FundingSourceSection: React.FC<FundingSourceSectionProps> = ({
  governmentScholarships,
  universityScholarships,
  organizationScholarships,
  loading,
  onScholarshipClick
}) => {
  return (
    <section className="py-10 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900">
            Par Origine de Financement
          </h2>
        </div>

        {/* Funding sources */}
        <div className="space-y-10">
          {/* Government Section */}
          <div>
            <div className="flex items-center mb-4">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 mr-3">
                <Government className="w-4 h-4 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">Bourses des Gouvernements</h3>
            </div>

            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {[...Array(3)].map((_, index) => (
                  <div key={index} className="bg-white border border-gray-200 rounded-md animate-pulse">
                    <div className="h-40 bg-gray-200"></div>
                    <div className="p-4">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {governmentScholarships.slice(0, 3).map((scholarship) => (
                  <ScholarshipCard
                    key={scholarship.id}
                    id={scholarship.id}
                    title={scholarship.title}
                    thumbnail={scholarship.thumbnail}
                    deadline={scholarship.deadline}
                    isOpen={scholarship.isOpen}
                    country={scholarship.country}
                    onClick={onScholarshipClick}
                  />
                ))}
              </div>
            )}

            <div className="mt-4 text-right">
              <Link
                to="/scholarships?source=Gouvernement"
                className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800"
              >
                Voir toutes les bourses gouvernementales
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </Link>
            </div>
          </div>

          {/* University Section */}
          <div>
            <div className="flex items-center mb-4">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-purple-100 mr-3">
                <Building className="w-4 h-4 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">Bourses des Universités</h3>
            </div>

            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {[...Array(3)].map((_, index) => (
                  <div key={index} className="bg-white border border-gray-200 rounded-md animate-pulse">
                    <div className="h-40 bg-gray-200"></div>
                    <div className="p-4">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {universityScholarships.slice(0, 3).map((scholarship) => (
                  <ScholarshipCard
                    key={scholarship.id}
                    id={scholarship.id}
                    title={scholarship.title}
                    thumbnail={scholarship.thumbnail}
                    deadline={scholarship.deadline}
                    isOpen={scholarship.isOpen}
                    country={scholarship.country}
                    onClick={onScholarshipClick}
                  />
                ))}
              </div>
            )}

            <div className="mt-4 text-right">
              <Link
                to="/scholarships?source=Université"
                className="inline-flex items-center text-sm font-medium text-purple-600 hover:text-purple-800"
              >
                Voir toutes les bourses universitaires
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </Link>
            </div>
          </div>

          {/* Organization Section */}
          <div>
            <div className="flex items-center mb-4">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-100 mr-3">
                <Organization className="w-4 h-4 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">Bourses des Organisations</h3>
            </div>

            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {[...Array(3)].map((_, index) => (
                  <div key={index} className="bg-white border border-gray-200 rounded-md animate-pulse">
                    <div className="h-40 bg-gray-200"></div>
                    <div className="p-4">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {organizationScholarships.slice(0, 3).map((scholarship) => (
                  <ScholarshipCard
                    key={scholarship.id}
                    id={scholarship.id}
                    title={scholarship.title}
                    thumbnail={scholarship.thumbnail}
                    deadline={scholarship.deadline}
                    isOpen={scholarship.isOpen}
                    country={scholarship.country}
                    onClick={onScholarshipClick}
                  />
                ))}
              </div>
            )}

            <div className="mt-4 text-right">
              <Link
                to="/scholarships?source=Organisation"
                className="inline-flex items-center text-sm font-medium text-green-600 hover:text-green-800"
              >
                Voir toutes les bourses d'organisations
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FundingSourceSection;
