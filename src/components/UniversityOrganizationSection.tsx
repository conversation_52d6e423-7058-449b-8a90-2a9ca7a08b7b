import React from 'react';
import { Link } from 'react-router-dom';
import ScholarshipCard from './ScholarshipCard';
import { Scholarship } from './ScholarshipGrid';
import { Building, Organization } from './icons/index';

interface UniversityOrganizationSectionProps {
  universityScholarships: Scholarship[];
  organizationScholarships: Scholarship[];
  loading: boolean;
  onScholarshipClick: (id: number) => void;
}

const UniversityOrganizationSection: React.FC<UniversityOrganizationSectionProps> = ({
  universityScholarships,
  organizationScholarships,
  loading,
  onScholarshipClick
}) => {
  return (
    <section className="py-12 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            🏛️ Universités et Organisations
          </h2>
          <p className="mt-2 text-gray-600">
            Bourses offertes par des universités et organisations du monde entier
          </p>
        </div>

        {/* University scholarships */}
        <div className="mb-12">
          <div className="flex items-center mb-6">
            <Building className="w-6 h-6 text-purple-600 mr-2" />
            <h3 className="text-xl font-bold text-gray-900">Bourses des Universités</h3>
          </div>

          {loading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(3)].map((_, index) => (
                <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden animate-pulse">
                  <div className="aspect-[16/9] bg-gray-200"></div>
                  <div className="p-4">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2 mb-3"></div>
                    <div className="flex justify-between mt-4">
                      <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {universityScholarships.slice(0, 3).map((scholarship) => (
                <ScholarshipCard
                  key={scholarship.id}
                  id={scholarship.id}
                  title={scholarship.title}
                  thumbnail={scholarship.thumbnail}
                  deadline={scholarship.deadline}
                  isOpen={scholarship.isOpen}
                  country={scholarship.country}
                  onClick={onScholarshipClick}
                />
              ))}
            </div>
          )}

          <div className="flex justify-end mt-4">
            <Link
              to="/scholarships?source=Université"
              className="inline-flex items-center text-sm font-medium text-purple-600 hover:text-purple-800"
            >
              Voir toutes les bourses universitaires
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </Link>
          </div>
        </div>

        {/* Organization scholarships */}
        <div>
          <div className="flex items-center mb-6">
            <Organization className="w-6 h-6 text-green-600 mr-2" />
            <h3 className="text-xl font-bold text-gray-900">Bourses des Organisations</h3>
          </div>

          {loading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(3)].map((_, index) => (
                <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden animate-pulse">
                  <div className="aspect-[16/9] bg-gray-200"></div>
                  <div className="p-4">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2 mb-3"></div>
                    <div className="flex justify-between mt-4">
                      <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {organizationScholarships.slice(0, 3).map((scholarship) => (
                <ScholarshipCard
                  key={scholarship.id}
                  id={scholarship.id}
                  title={scholarship.title}
                  thumbnail={scholarship.thumbnail}
                  deadline={scholarship.deadline}
                  isOpen={scholarship.isOpen}
                  country={scholarship.country}
                  onClick={onScholarshipClick}
                />
              ))}
            </div>
          )}

          <div className="flex justify-end mt-4">
            <Link
              to="/scholarships?source=Organisation"
              className="inline-flex items-center text-sm font-medium text-green-600 hover:text-green-800"
            >
              Voir toutes les bourses d'organisations
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default UniversityOrganizationSection;
